import fetch from 'node-fetch';
import { ErrorHandler } from '../utils/ErrorHandler.js';
export class DeepseekProvider {
    name = 'deepseek';
    models = ['deepseek-chat', 'deepseek-reasoner'];
    apiKey;
    baseUrl;
    errorHandler;
    constructor(api<PERSON><PERSON>, errorHandler) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://api.deepseek.com/v1';
        this.errorHandler = errorHandler || new ErrorHandler({
            maxRetries: 3,
            baseDelay: 1000,
            maxDelay: 30000,
            backoffMultiplier: 2,
            retryableErrors: ['rate_limit_exceeded', 'server_error', 'service_unavailable']
        });
    }
    async isAvailable() {
        try {
            if (!this.apiKey)
                return false;
            // For development/testing, if we have an API key, consider it available
            // The actual validation will happen when making real API calls
            if (this.apiKey && this.apiKey.length > 10) {
                return true;
            }
            return false;
        }
        catch (error) {
            return false;
        }
    }
    async testConnection() {
        try {
            if (!this.apiKey) {
                return { available: false, error: 'No API key configured' };
            }
            const response = await fetch(`${this.baseUrl}/models`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                signal: AbortSignal.timeout(10000) // 10 second timeout
            });
            if (response.ok) {
                return { available: true };
            }
            else {
                const errorText = await response.text().catch(() => 'Unknown error');
                return {
                    available: false,
                    error: `API returned ${response.status}: ${errorText}`
                };
            }
        }
        catch (error) {
            const err = error;
            if (err.name === 'AbortError') {
                return { available: false, error: 'Connection timeout - check your internet connection' };
            }
            return {
                available: false,
                error: `Network error: ${err.message}`
            };
        }
    }
    async generateResponse(messages, options) {
        this.errorHandler.validateApiKey(this.apiKey, this.name);
        const requestBody = {
            model: options?.tools ? 'deepseek-chat' : 'deepseek-chat', // Use chat model for function calling
            messages: this.formatMessages(messages),
            temperature: options?.temperature ?? 0.7,
            max_tokens: options?.maxTokens ?? 4096,
            stream: false,
            ...(options?.tools && {
                tools: options.tools,
                tool_choice: options.toolChoice ?? 'auto'
            })
        };
        return this.errorHandler.withRetry(async () => {
            const response = await fetch(`${this.baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`Deepseek API error: ${response.status} - ${errorData?.error?.message || response.statusText}`);
            }
            const data = await response.json();
            const choice = data.choices?.[0];
            if (!choice) {
                throw new Error('No response choice received from Deepseek API');
            }
            return {
                content: choice.message?.content || '',
                toolCalls: choice.message?.tool_calls?.map((tc) => ({
                    id: tc.id,
                    type: tc.type,
                    function: {
                        name: tc.function.name,
                        arguments: tc.function.arguments
                    }
                })) || [],
                usage: data.usage ? {
                    promptTokens: data.usage.prompt_tokens,
                    completionTokens: data.usage.completion_tokens,
                    totalTokens: data.usage.total_tokens
                } : undefined
            };
        }, {
            operation: 'deepseek_generate_response',
            provider: this.name,
            model: requestBody.model,
            timestamp: new Date(),
            retryCount: 0
        });
    }
    async *streamResponse(messages, options) {
        this.errorHandler.validateApiKey(this.apiKey, this.name);
        const requestBody = {
            model: options?.tools ? 'deepseek-chat' : 'deepseek-chat',
            messages: this.formatMessages(messages),
            temperature: options?.temperature ?? 0.7,
            max_tokens: options?.maxTokens ?? 4096,
            stream: true,
            ...(options?.tools && {
                tools: options.tools,
                tool_choice: options.toolChoice ?? 'auto'
            })
        };
        const response = await this.errorHandler.withRetry(async () => {
            const res = await fetch(`${this.baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });
            if (!res.ok) {
                const errorData = await res.json().catch(() => ({}));
                throw new Error(`Deepseek API error: ${res.status} - ${errorData?.error?.message || res.statusText}`);
            }
            return res;
        }, {
            operation: 'deepseek_stream_response',
            provider: this.name,
            model: requestBody.model,
            timestamp: new Date(),
            retryCount: 0
        });
        if (!response.body) {
            throw new Error('No response body received from Deepseek API');
        }
        const reader = response.body?.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    yield { done: true };
                    break;
                }
                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';
                for (const line of lines) {
                    const trimmed = line.trim();
                    if (!trimmed || !trimmed.startsWith('data: '))
                        continue;
                    const data = trimmed.slice(6);
                    if (data === '[DONE]') {
                        yield { done: true };
                        return;
                    }
                    try {
                        const parsed = JSON.parse(data);
                        const choice = parsed.choices?.[0];
                        if (choice) {
                            const delta = choice.delta;
                            yield {
                                content: delta?.content || '',
                                toolCalls: delta?.tool_calls?.map((tc) => ({
                                    id: tc.id,
                                    type: tc.type,
                                    function: {
                                        name: tc.function?.name,
                                        arguments: tc.function?.arguments
                                    }
                                })) || [],
                                done: false
                            };
                        }
                    }
                    catch (error) {
                        // Skip invalid JSON lines
                        continue;
                    }
                }
            }
        }
        finally {
            reader.releaseLock();
        }
    }
    formatMessages(messages) {
        return messages.map(msg => {
            const formatted = {
                role: msg.role,
                content: msg.content
            };
            if (msg.toolCalls && msg.toolCalls.length > 0) {
                formatted.tool_calls = msg.toolCalls.map(tc => ({
                    id: tc.id,
                    type: tc.type,
                    function: {
                        name: tc.function.name,
                        arguments: tc.function.arguments
                    }
                }));
            }
            if (msg.toolCallId) {
                formatted.tool_call_id = msg.toolCallId;
            }
            return formatted;
        });
    }
    setApiKey(apiKey) {
        this.apiKey = apiKey;
    }
    setBaseUrl(baseUrl) {
        this.baseUrl = baseUrl;
    }
    async getModels() {
        try {
            if (!this.apiKey)
                return this.models;
            const response = await fetch(`${this.baseUrl}/models`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                return this.models; // Fallback to default models
            }
            const data = await response.json();
            const availableModels = data.data?.map((model) => model.id) || [];
            // Filter to only Deepseek models
            return availableModels.filter((model) => model.includes('deepseek'));
        }
        catch (error) {
            return this.models; // Fallback to default models
        }
    }
    getSystemPrompt() {
        return `You are Arien AI, an intelligent CLI assistant that can execute shell commands to help users accomplish their tasks.

CORE CAPABILITIES:
- Execute shell commands safely and efficiently
- Analyze command outputs and provide insights
- Handle errors gracefully with retry logic
- Provide step-by-step guidance for complex tasks
- Maintain context across multiple command executions

IMPORTANT GUIDELINES:
1. Always explain what you're about to do before executing commands
2. Use the shell tool for any system operations, file manipulations, or external tool usage
3. Check command results and handle errors appropriately
4. Ask for confirmation before executing potentially destructive commands
5. Provide clear, actionable feedback based on command outputs
6. Break down complex tasks into smaller, manageable steps
7. Use appropriate error handling and retry logic for network operations

FUNCTION CALLING USAGE:
- Use execute_shell_command for all system operations
- Set requireApproval=true for destructive operations
- Use appropriate timeouts for long-running commands
- Provide meaningful descriptions for complex commands
- Handle both sequential and parallel command execution as needed

WHEN TO USE SHELL COMMANDS:
- File system operations (ls, mkdir, cp, mv, rm)
- Package management (npm, pip, apt, brew)
- Git operations (clone, commit, push, pull)
- System monitoring (ps, top, df, free)
- Network operations (curl, wget, ping)
- Development tasks (build, test, deploy)

Remember: Always prioritize user safety and system stability. Explain your actions clearly and handle errors gracefully.`;
    }
}
//# sourceMappingURL=DeepseekProvider.js.map