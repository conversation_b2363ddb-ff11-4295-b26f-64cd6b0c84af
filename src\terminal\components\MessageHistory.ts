import { ChatMessage } from '../../types/index.js';
import { ConfigManager } from '../../config/ConfigManager.js';
import chalk from 'chalk';

export interface HistoryEntry {
  id: string;
  timestamp: Date;
  sessionId?: string;
  message: ChatMessage;
  response?: ChatMessage;
  executionTime?: number;
}

export class MessageHistory {
  private history: HistoryEntry[] = [];
  private currentIndex = -1;
  private configManager: ConfigManager;
  private maxHistorySize = 1000;

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
    this.loadHistory();
  }

  public addEntry(message: ChatMessage, response?: ChatMessage, sessionId?: string): string {
    const entry: HistoryEntry = {
      id: this.generateId(),
      timestamp: new Date(),
      sessionId: sessionId || undefined,
      message,
      response,
      executionTime: undefined // This would be set by the caller if needed
    };

    this.history.push(entry);
    this.currentIndex = this.history.length;

    // Trim history if it exceeds max size
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(-this.maxHistorySize);
    }

    if (this.configManager.shouldSaveHistory()) {
      this.saveHistory();
    }

    return entry.id;
  }

  public getEntry(id: string): HistoryEntry | undefined {
    return this.history.find(entry => entry.id === id);
  }

  public getAllEntries(): HistoryEntry[] {
    return [...this.history];
  }

  public getEntriesForSession(sessionId: string): HistoryEntry[] {
    return this.history.filter(entry => entry.sessionId === sessionId);
  }

  public getRecentEntries(count: number): HistoryEntry[] {
    return this.history.slice(-count);
  }

  public searchEntries(query: string): HistoryEntry[] {
    const lowerQuery = query.toLowerCase();
    return this.history.filter(entry => 
      entry.message.content.toLowerCase().includes(lowerQuery) ||
      (entry.response?.content.toLowerCase().includes(lowerQuery))
    );
  }

  public navigateHistory(direction: 'up' | 'down'): string | null {
    if (this.history.length === 0) return null;

    if (direction === 'up') {
      this.currentIndex = Math.max(0, this.currentIndex - 1);
    } else {
      this.currentIndex = Math.min(this.history.length - 1, this.currentIndex + 1);
    }

    const entry = this.history[this.currentIndex];
    return entry ? entry.message.content : null;
  }

  public getCurrentHistoryIndex(): number {
    return this.currentIndex;
  }

  public resetHistoryNavigation(): void {
    this.currentIndex = this.history.length;
  }

  public clearHistory(): void {
    this.history = [];
    this.currentIndex = -1;
    if (this.configManager.shouldSaveHistory()) {
      this.saveHistory();
    }
  }

  public clearSessionHistory(sessionId: string): void {
    this.history = this.history.filter(entry => entry.sessionId !== sessionId);
    this.currentIndex = this.history.length;
    if (this.configManager.shouldSaveHistory()) {
      this.saveHistory();
    }
  }

  public exportHistory(format: 'json' | 'text' = 'json'): string {
    if (format === 'json') {
      return JSON.stringify(this.history, null, 2);
    } else {
      return this.history.map(entry => {
        const timestamp = entry.timestamp.toLocaleString();
        const session = entry.sessionId ? ` [${entry.sessionId}]` : '';
        const user = `👤 User: ${entry.message.content}`;
        const ai = entry.response ? `🤖 AI: ${entry.response.content}` : '';
        return `${timestamp}${session}\n${user}\n${ai}\n---`;
      }).join('\n\n');
    }
  }

  public importHistory(data: string, format: 'json' | 'text' = 'json'): void {
    try {
      if (format === 'json') {
        const imported = JSON.parse(data) as HistoryEntry[];
        this.history = [...this.history, ...imported];
      } else {
        // Simple text import - this would need more sophisticated parsing
        throw new Error('Text import not yet implemented');
      }

      // Sort by timestamp
      this.history.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
      
      // Trim if necessary
      if (this.history.length > this.maxHistorySize) {
        this.history = this.history.slice(-this.maxHistorySize);
      }

      this.currentIndex = this.history.length;
      
      if (this.configManager.shouldSaveHistory()) {
        this.saveHistory();
      }
    } catch (error) {
      throw new Error(`Failed to import history: ${(error as Error).message}`);
    }
  }

  public getStatistics(): {
    totalEntries: number;
    sessionsCount: number;
    averageResponseTime: number;
    mostActiveSession: string | null;
    oldestEntry: Date | null;
    newestEntry: Date | null;
  } {
    if (this.history.length === 0) {
      return {
        totalEntries: 0,
        sessionsCount: 0,
        averageResponseTime: 0,
        mostActiveSession: null,
        oldestEntry: null,
        newestEntry: null
      };
    }

    const sessions = new Set(this.history.map(entry => entry.sessionId).filter(Boolean));
    const responseTimes = this.history
      .map(entry => entry.executionTime)
      .filter(time => time !== undefined) as number[];
    
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0;

    // Find most active session
    const sessionCounts = new Map<string, number>();
    this.history.forEach(entry => {
      if (entry.sessionId) {
        sessionCounts.set(entry.sessionId, (sessionCounts.get(entry.sessionId) || 0) + 1);
      }
    });

    let mostActiveSession: string | null = null;
    let maxCount = 0;
    sessionCounts.forEach((count, sessionId) => {
      if (count > maxCount) {
        maxCount = count;
        mostActiveSession = sessionId;
      }
    });

    const timestamps = this.history.map(entry => entry.timestamp);
    const oldestEntry = new Date(Math.min(...timestamps.map(t => t.getTime())));
    const newestEntry = new Date(Math.max(...timestamps.map(t => t.getTime())));

    return {
      totalEntries: this.history.length,
      sessionsCount: sessions.size,
      averageResponseTime,
      mostActiveSession,
      oldestEntry,
      newestEntry
    };
  }

  public formatHistoryForDisplay(entries?: HistoryEntry[]): string {
    const entriesToShow = entries || this.getRecentEntries(10);
    
    if (entriesToShow.length === 0) {
      return chalk.yellow('No history entries found.');
    }

    let output = chalk.cyan.bold('📜 Message History\n\n');
    
    entriesToShow.forEach((entry, index) => {
      const timestamp = this.configManager.shouldShowTimestamps() 
        ? chalk.gray(`[${entry.timestamp.toLocaleTimeString()}] `)
        : '';
      
      const sessionInfo = entry.sessionId 
        ? chalk.blue(`[${entry.sessionId}] `)
        : '';
      
      output += `${chalk.gray(`${index + 1}.`)} ${timestamp}${sessionInfo}\n`;
      output += `${chalk.blue('👤 You:')} ${entry.message.content}\n`;
      
      if (entry.response) {
        output += `${chalk.green('🤖 AI:')} ${entry.response.content.substring(0, 100)}${entry.response.content.length > 100 ? '...' : ''}\n`;
      }
      
      if (entry.executionTime) {
        output += chalk.gray(`   ⏱️  ${entry.executionTime}ms\n`);
      }
      
      output += '\n';
    });

    return output;
  }

  private generateId(): string {
    return `hist_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private loadHistory(): void {
    if (!this.configManager.shouldSaveHistory()) return;

    try {
      // This would load from a persistent storage
      // For now, we'll keep it in memory only
      // In a real implementation, you'd load from a file or database
    } catch (error) {
      // Ignore loading errors
    }
  }

  private saveHistory(): void {
    if (!this.configManager.shouldSaveHistory()) return;

    try {
      // This would save to persistent storage
      // For now, we'll keep it in memory only
      // In a real implementation, you'd save to a file or database
    } catch (error) {
      // Ignore saving errors
    }
  }

  public setMaxHistorySize(size: number): void {
    this.maxHistorySize = Math.max(1, size);
    
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(-this.maxHistorySize);
      this.currentIndex = Math.min(this.currentIndex, this.history.length - 1);
    }
  }

  public getMaxHistorySize(): number {
    return this.maxHistorySize;
  }
}
