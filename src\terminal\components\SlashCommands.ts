import { ConfigManager } from '../../config/ConfigManager.js';
import { TerminalSession } from '../../types/index.js';
import chalk from 'chalk';

export interface SlashCommandResult {
  success: boolean;
  message: string;
  shouldExit?: boolean;
  shouldClear?: boolean;
  configChanged?: boolean;
}

export class SlashCommands {
  private configManager: ConfigManager;
  private availableProviders: string[] = ['deepseek', 'ollama'];
  private availableModels: Map<string, string[]> = new Map([
    ['deepseek', ['deepseek-chat', 'deepseek-reasoner']],
    ['ollama', []] // Will be populated dynamically
  ]);

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
  }

  public async executeCommand(input: string): Promise<SlashCommandResult> {
    const parts = input.slice(1).trim().split(/\s+/);
    const command = parts[0].toLowerCase();
    const args = parts.slice(1);

    switch (command) {
      case 'help':
      case 'h':
        return this.showHelp();
      
      case 'model':
      case 'm':
        return await this.switchModel(args[0]);
      
      case 'provider':
      case 'p':
        return await this.switchProvider(args[0]);
      
      case 'session':
      case 's':
        return this.manageSession(args);
      
      case 'history':
      case 'hist':
        return this.showHistory();
      
      case 'clear':
      case 'c':
        return this.clearConversation();
      
      case 'config':
      case 'cfg':
        return this.showConfig(args[0]);
      
      case 'set':
        return this.setConfig(args);

      case 'api-key':
      case 'apikey':
        return this.setApiKey(args[0]);

      case 'models':
        return await this.listModels(args[0]);
      
      case 'providers':
        return this.listProviders();
      
      case 'sessions':
        return this.listSessions();
      
      case 'export':
        return this.exportConfig();
      
      case 'import':
        return this.importConfig(args.join(' '));
      
      case 'reset':
        return this.resetConfig();
      
      case 'exit':
      case 'quit':
      case 'q':
        return this.exit();
      
      default:
        return {
          success: false,
          message: chalk.red(`Unknown command: ${command}. Type /help for available commands.`)
        };
    }
  }

  private showHelp(): SlashCommandResult {
    const helpText = `
${chalk.bold.cyan('🤖 Arien AI - Slash Commands Help')}

${chalk.bold('Model & Provider Management:')}
  ${chalk.green('/model <name>')}        Switch to a different model
  ${chalk.green('/provider <name>')}     Switch to a different provider (deepseek, ollama)
  ${chalk.green('/models [provider]')}   List available models for provider
  ${chalk.green('/providers')}           List all available providers

${chalk.bold('Session Management:')}
  ${chalk.green('/session <name>')}      Create or switch to a session
  ${chalk.green('/session list')}        List all sessions
  ${chalk.green('/session delete <id>')} Delete a session
  ${chalk.green('/sessions')}            List all sessions (alias)

${chalk.bold('Configuration:')}
  ${chalk.green('/config [key]')}        Show configuration (or specific key)
  ${chalk.green('/set <key> <value>')}   Set configuration value
  ${chalk.green('/api-key <key>')}       Set API key for current provider
  ${chalk.green('/export')}              Export configuration to JSON
  ${chalk.green('/import <json>')}       Import configuration from JSON
  ${chalk.green('/reset')}               Reset to default configuration

${chalk.bold('Conversation:')}
  ${chalk.green('/history')}             Show message history
  ${chalk.green('/clear')}               Clear current conversation
  
${chalk.bold('System:')}
  ${chalk.green('/help')}                Show this help message
  ${chalk.green('/exit')}                Exit the application

${chalk.bold('Configuration Keys:')}
  ${chalk.yellow('temperature')}         AI response randomness (0.0-2.0)
  ${chalk.yellow('max-tokens')}          Maximum response length
  ${chalk.yellow('auto-approve')}        Auto-approve shell commands (true/false)
  ${chalk.yellow('theme')}               UI theme (dark/light)
  ${chalk.yellow('timestamps')}          Show timestamps (true/false)
  ${chalk.yellow('save-history')}        Save conversation history (true/false)

${chalk.bold('Examples:')}
  ${chalk.gray('/model deepseek-reasoner')}
  ${chalk.gray('/provider ollama')}
  ${chalk.gray('/set temperature 0.8')}
  ${chalk.gray('/session my-project')}
  ${chalk.gray('/config temperature')}
`;

    return {
      success: true,
      message: helpText
    };
  }

  private async switchModel(modelName?: string): Promise<SlashCommandResult> {
    if (!modelName) {
      const currentModel = this.configManager.getModel();
      const currentProvider = this.configManager.getProvider();
      const availableModels = this.availableModels.get(currentProvider) || [];
      
      return {
        success: true,
        message: `Current model: ${chalk.green(currentModel)}\nAvailable models for ${currentProvider}: ${availableModels.map(m => chalk.yellow(m)).join(', ')}`
      };
    }

    const currentProvider = this.configManager.getProvider();
    const availableModels = this.availableModels.get(currentProvider) || [];
    
    if (!availableModels.includes(modelName)) {
      return {
        success: false,
        message: chalk.red(`Model "${modelName}" is not available for provider "${currentProvider}".\nAvailable models: ${availableModels.join(', ')}`)
      };
    }

    this.configManager.setModel(modelName);
    
    return {
      success: true,
      message: chalk.green(`✅ Switched to model: ${modelName}`),
      configChanged: true
    };
  }

  private async switchProvider(providerName?: string): Promise<SlashCommandResult> {
    if (!providerName) {
      const currentProvider = this.configManager.getProvider();
      return {
        success: true,
        message: `Current provider: ${chalk.green(currentProvider)}\nAvailable providers: ${this.availableProviders.map(p => chalk.yellow(p)).join(', ')}`
      };
    }

    if (!this.availableProviders.includes(providerName)) {
      return {
        success: false,
        message: chalk.red(`Provider "${providerName}" is not available.\nAvailable providers: ${this.availableProviders.join(', ')}`)
      };
    }

    this.configManager.setProvider(providerName);
    
    // Set default model for the provider
    const defaultModels = this.availableModels.get(providerName) || [];
    if (defaultModels.length > 0) {
      this.configManager.setModel(defaultModels[0]);
    }

    return {
      success: true,
      message: chalk.green(`✅ Switched to provider: ${providerName}${defaultModels.length > 0 ? ` with model: ${defaultModels[0]}` : ''}`),
      configChanged: true
    };
  }

  private manageSession(args: string[]): SlashCommandResult {
    const action = args[0];
    
    if (!action) {
      return {
        success: false,
        message: chalk.red('Session command requires an action. Use: /session <name> or /session list')
      };
    }

    if (action === 'list') {
      return this.listSessions();
    }

    if (action === 'delete') {
      const sessionId = args[1];
      if (!sessionId) {
        return {
          success: false,
          message: chalk.red('Delete requires a session ID. Use: /session delete <id>')
        };
      }

      this.configManager.deleteSession(sessionId);
      return {
        success: true,
        message: chalk.green(`✅ Deleted session: ${sessionId}`)
      };
    }

    // Create or switch to session
    const sessionName = action;
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    const session: TerminalSession = {
      id: sessionId,
      name: sessionName,
      createdAt: new Date(),
      lastActivity: new Date(),
      messageCount: 0,
      provider: this.configManager.getProvider(),
      model: this.configManager.getModel()
    };

    this.configManager.saveSession(session);

    return {
      success: true,
      message: chalk.green(`✅ Created/switched to session: ${sessionName} (${sessionId})`)
    };
  }

  private showHistory(): SlashCommandResult {
    // This would integrate with actual message history
    return {
      success: true,
      message: chalk.blue('📜 Message history feature will be implemented with the main terminal interface.')
    };
  }

  private clearConversation(): SlashCommandResult {
    return {
      success: true,
      message: chalk.green('✅ Conversation cleared'),
      shouldClear: true
    };
  }

  private showConfig(key?: string): SlashCommandResult {
    const config = this.configManager.getConfig();
    
    if (key) {
      const value = (config as any)[key];
      if (value === undefined) {
        return {
          success: false,
          message: chalk.red(`Configuration key "${key}" not found`)
        };
      }
      
      return {
        success: true,
        message: `${chalk.yellow(key)}: ${chalk.green(String(value))}`
      };
    }

    const configText = `
${chalk.bold.cyan('🔧 Current Configuration:')}

${chalk.yellow('provider')}: ${chalk.green(config.provider)}
${chalk.yellow('model')}: ${chalk.green(config.model)}
${chalk.yellow('temperature')}: ${chalk.green(config.temperature.toString())}
${chalk.yellow('max-tokens')}: ${chalk.green(config.maxTokens.toString())}
${chalk.yellow('auto-approve')}: ${chalk.green(config.autoApprove.toString())}
${chalk.yellow('theme')}: ${chalk.green(config.theme)}
${chalk.yellow('timestamps')}: ${chalk.green(config.showTimestamps.toString())}
${chalk.yellow('save-history')}: ${chalk.green(config.saveHistory.toString())}
`;

    return {
      success: true,
      message: configText
    };
  }

  private setApiKey(apiKey?: string): SlashCommandResult {
    if (!apiKey) {
      return {
        success: false,
        message: chalk.red('API key is required. Use: /api-key <your-api-key>')
      };
    }

    this.configManager.setApiKey(apiKey);

    return {
      success: true,
      message: chalk.green('✅ API key set successfully'),
      configChanged: true
    };
  }

  private setConfig(args: string[]): SlashCommandResult {
    if (args.length < 2) {
      return {
        success: false,
        message: chalk.red('Set command requires key and value. Use: /set <key> <value>')
      };
    }

    const key = args[0];
    const value = args.slice(1).join(' ');

    try {
      switch (key) {
        case 'temperature':
          const temp = parseFloat(value);
          if (isNaN(temp) || temp < 0 || temp > 2) {
            throw new Error('Temperature must be a number between 0 and 2');
          }
          this.configManager.setTemperature(temp);
          break;

        case 'max-tokens':
          const tokens = parseInt(value);
          if (isNaN(tokens) || tokens < 1) {
            throw new Error('Max tokens must be a positive number');
          }
          this.configManager.setMaxTokens(tokens);
          break;

        case 'auto-approve':
          const autoApprove = value.toLowerCase() === 'true';
          this.configManager.setAutoApprove(autoApprove);
          break;

        case 'theme':
          if (value !== 'dark' && value !== 'light') {
            throw new Error('Theme must be "dark" or "light"');
          }
          this.configManager.setTheme(value as 'dark' | 'light');
          break;

        case 'timestamps':
          const showTimestamps = value.toLowerCase() === 'true';
          this.configManager.setShowTimestamps(showTimestamps);
          break;

        case 'save-history':
          const saveHistory = value.toLowerCase() === 'true';
          this.configManager.setSaveHistory(saveHistory);
          break;

        case 'api-key':
        case 'apikey':
          this.configManager.setApiKey(value);
          break;

        default:
          throw new Error(`Unknown configuration key: ${key}`);
      }

      return {
        success: true,
        message: chalk.green(`✅ Set ${key} = ${value}`),
        configChanged: true
      };
    } catch (error) {
      return {
        success: false,
        message: chalk.red(`Error setting ${key}: ${(error as Error).message}`)
      };
    }
  }

  private async listModels(provider?: string): Promise<SlashCommandResult> {
    const targetProvider = provider || this.configManager.getProvider();
    const models = this.availableModels.get(targetProvider) || [];
    
    if (models.length === 0) {
      return {
        success: true,
        message: chalk.yellow(`No models available for provider: ${targetProvider}`)
      };
    }

    const currentModel = this.configManager.getModel();
    const modelList = models.map(model => 
      model === currentModel ? chalk.green(`${model} (current)`) : chalk.yellow(model)
    ).join('\n  ');

    return {
      success: true,
      message: `${chalk.bold.cyan(`Models for ${targetProvider}:`)}\n  ${modelList}`
    };
  }

  private listProviders(): SlashCommandResult {
    const currentProvider = this.configManager.getProvider();
    const providerList = this.availableProviders.map(provider =>
      provider === currentProvider ? chalk.green(`${provider} (current)`) : chalk.yellow(provider)
    ).join('\n  ');

    return {
      success: true,
      message: `${chalk.bold.cyan('Available Providers:')}\n  ${providerList}`
    };
  }

  private listSessions(): SlashCommandResult {
    const sessions = this.configManager.getSessions();
    const sessionEntries = Object.values(sessions);
    
    if (sessionEntries.length === 0) {
      return {
        success: true,
        message: chalk.yellow('No sessions found')
      };
    }

    const sessionList = sessionEntries.map(session => 
      `${chalk.green(session.name)} (${chalk.gray(session.id)}) - ${chalk.yellow(session.messageCount)} messages - ${chalk.blue(session.lastActivity.toLocaleString())}`
    ).join('\n  ');

    return {
      success: true,
      message: `${chalk.bold.cyan('Sessions:')}\n  ${sessionList}`
    };
  }

  private exportConfig(): SlashCommandResult {
    try {
      const configJson = this.configManager.exportConfig();
      return {
        success: true,
        message: `${chalk.green('✅ Configuration exported:')}\n${chalk.gray(configJson)}`
      };
    } catch (error) {
      return {
        success: false,
        message: chalk.red(`Error exporting configuration: ${(error as Error).message}`)
      };
    }
  }

  private importConfig(configJson: string): SlashCommandResult {
    if (!configJson) {
      return {
        success: false,
        message: chalk.red('Import requires JSON configuration. Use: /import <json>')
      };
    }

    try {
      this.configManager.importConfig(configJson);
      return {
        success: true,
        message: chalk.green('✅ Configuration imported successfully'),
        configChanged: true
      };
    } catch (error) {
      return {
        success: false,
        message: chalk.red(`Error importing configuration: ${(error as Error).message}`)
      };
    }
  }

  private resetConfig(): SlashCommandResult {
    this.configManager.reset();
    return {
      success: true,
      message: chalk.green('✅ Configuration reset to defaults'),
      configChanged: true
    };
  }

  private exit(): SlashCommandResult {
    return {
      success: true,
      message: chalk.green('👋 Goodbye!'),
      shouldExit: true
    };
  }

  public updateAvailableModels(provider: string, models: string[]): void {
    this.availableModels.set(provider, models);
  }

  public isSlashCommand(input: string): boolean {
    return input.startsWith('/') && input.length > 1;
  }

  public getAutoCompleteOptions(input: string): string[] {
    const commands = [
      '/help', '/model', '/provider', '/session', '/history', '/clear',
      '/config', '/set', '/api-key', '/models', '/providers', '/sessions',
      '/export', '/import', '/reset', '/exit'
    ];

    if (input === '/') {
      return commands;
    }

    return commands.filter(cmd => cmd.startsWith(input));
  }
}
