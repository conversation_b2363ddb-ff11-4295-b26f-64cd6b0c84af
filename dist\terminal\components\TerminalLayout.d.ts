import blessed from 'blessed';
import { TerminalConfig } from '../../types/index.js';
export declare class TerminalLayout {
    screen: blessed.Widgets.Screen;
    chatBox: blessed.Widgets.Log;
    inputBox: blessed.Widgets.TextboxElement;
    headerBox: blessed.Widgets.BoxElement;
    statusBox: blessed.Widgets.BoxElement;
    helpBox: blessed.Widgets.BoxElement;
    private config;
    constructor(config: TerminalConfig);
    private initializeScreen;
    private createLayout;
    private setupKeyBindings;
    private applyTheme;
    private getHeaderContent;
    private getStatusContent;
    private getHelpContent;
    updateHeader(provider: string, model: string): void;
    updateStatus(): void;
    appendMessage(message: string): void;
    clearChat(): void;
    toggleHelp(): void;
    toggleTheme(): void;
    setInputPlaceholder(placeholder: string): void;
    focusInput(): void;
    render(): void;
    destroy(): void;
    private listeners;
    on(event: string, listener: Function): void;
    emit(event: string, ...args: any[]): void;
}
//# sourceMappingURL=TerminalLayout.d.ts.map