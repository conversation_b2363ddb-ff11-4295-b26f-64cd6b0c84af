import { LLMProvider, ChatMessage, LLMResponse, LLMStreamChunk, GenerationOptions } from '../types/index.js';
import { ErrorHandler } from '../utils/ErrorHandler.js';
export declare class DeepseekProvider implements LLMProvider {
    name: string;
    models: string[];
    apiKey?: string;
    baseUrl: string;
    private errorHandler;
    constructor(apiKey?: string, errorHandler?: ErrorHandler);
    isAvailable(): Promise<boolean>;
    testConnection(): Promise<{
        available: boolean;
        error?: string;
    }>;
    generateResponse(messages: ChatMessage[], options?: GenerationOptions): Promise<LLMResponse>;
    streamResponse(messages: ChatMessage[], options?: GenerationOptions): AsyncGenerator<LLMStreamChunk>;
    private formatMessages;
    setApiKey(apiKey: string): void;
    setBaseUrl(baseUrl: string): void;
    getModels(): Promise<string[]>;
    getSystemPrompt(): string;
}
//# sourceMappingURL=DeepseekProvider.d.ts.map