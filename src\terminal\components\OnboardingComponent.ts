import inquirer from 'inquirer';
import chalk from 'chalk';
import figlet from 'figlet';
import { ConfigManager } from '../../config/ConfigManager.js';
import { DeepseekProvider } from '../../providers/DeepseekProvider.js';
import { OllamaProvider } from '../../providers/OllamaProvider.js';
import { ErrorHandler } from '../../utils/ErrorHandler.js';

export interface OnboardingResult {
  provider: string;
  model: string;
  apiKey?: string;
  baseUrl?: string;
  completed: boolean;
}

export class OnboardingComponent {
  private configManager: ConfigManager;
  private errorHandler: ErrorHandler;

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
    this.errorHandler = new ErrorHandler(configManager.getRetryConfig());
  }

  public async run(): Promise<OnboardingResult> {
    console.clear();
    this.showWelcomeBanner();
    
    const result: OnboardingResult = {
      provider: '',
      model: '',
      completed: false
    };

    try {
      // Step 1: Choose provider
      result.provider = await this.chooseProvider();
      
      // Step 2: Configure provider
      if (result.provider === 'deepseek') {
        const deepseekConfig = await this.configureDeepseek();
        result.apiKey = deepseekConfig.apiKey;
        result.model = deepseekConfig.model;
      } else if (result.provider === 'ollama') {
        const ollamaConfig = await this.configureOllama();
        result.baseUrl = ollamaConfig.baseUrl;
        result.model = ollamaConfig.model;
      }

      // Step 3: Test configuration
      const testResult = await this.testConfiguration(result);
      if (!testResult.success) {
        console.log(chalk.yellow('\n⚠️  Configuration test failed, but you can continue and fix it later.'));
        console.log(chalk.gray(`Error: ${testResult.error}`));
      }

      // Step 4: Additional preferences
      await this.configurePreferences();

      // Step 5: Save configuration
      this.saveConfiguration(result);

      result.completed = true;
      this.showCompletionMessage();

    } catch (error) {
      console.log(chalk.red(`\n❌ Onboarding failed: ${(error as Error).message}`));
      console.log(chalk.gray('You can run the setup again later or configure manually.'));
    }

    return result;
  }

  private showWelcomeBanner(): void {
    const banner = figlet.textSync('Arien AI', {
      font: 'ANSI Shadow',
      horizontalLayout: 'default'
    });

    console.log(chalk.cyan(banner));
    console.log(chalk.white.bold('\n🤖 Welcome to Arien AI - Intelligent CLI Assistant\n'));
    console.log(chalk.gray('Let\'s set up your AI provider to get started.\n'));
    console.log(chalk.blue('This setup will only take a few minutes and you can change these settings later.\n'));
  }

  private async chooseProvider(): Promise<string> {
    console.log(chalk.bold('Step 1: Choose your AI Provider\n'));

    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Which AI provider would you like to use?',
        choices: [
          {
            name: '🧠 Deepseek (Cloud-based, requires API key)',
            value: 'deepseek',
            short: 'Deepseek'
          },
          {
            name: '🏠 Ollama (Local, requires Ollama installation)',
            value: 'ollama',
            short: 'Ollama'
          }
        ],
        default: 'deepseek'
      }
    ]);

    console.log(chalk.green(`✅ Selected provider: ${provider}\n`));
    return provider;
  }

  private async configureDeepseek(): Promise<{ apiKey: string; model: string }> {
    console.log(chalk.bold('Step 2: Configure Deepseek\n'));
    console.log(chalk.blue('You\'ll need a Deepseek API key. Get one at: https://platform.deepseek.com/\n'));

    const { apiKey } = await inquirer.prompt([
      {
        type: 'password',
        name: 'apiKey',
        message: 'Enter your Deepseek API key:',
        mask: '*',
        validate: (input: string) => {
          if (!input.trim()) {
            return 'API key is required';
          }
          if (input.length < 10) {
            return 'API key seems too short';
          }
          return true;
        }
      }
    ]);

    const { model } = await inquirer.prompt([
      {
        type: 'list',
        name: 'model',
        message: 'Choose your preferred model:',
        choices: [
          {
            name: 'deepseek-chat (Recommended for general use)',
            value: 'deepseek-chat',
            short: 'deepseek-chat'
          },
          {
            name: 'deepseek-reasoner (Advanced reasoning capabilities)',
            value: 'deepseek-reasoner',
            short: 'deepseek-reasoner'
          }
        ],
        default: 'deepseek-chat'
      }
    ]);

    console.log(chalk.green(`✅ Deepseek configured with model: ${model}\n`));
    return { apiKey, model };
  }

  private async configureOllama(): Promise<{ baseUrl: string; model: string }> {
    console.log(chalk.bold('Step 2: Configure Ollama\n'));
    console.log(chalk.blue('Make sure Ollama is installed and running. Get it at: https://ollama.ai/\n'));

    const { baseUrl } = await inquirer.prompt([
      {
        type: 'input',
        name: 'baseUrl',
        message: 'Ollama base URL:',
        default: 'http://localhost:11434',
        validate: (input: string) => {
          try {
            new URL(input);
            return true;
          } catch {
            return 'Please enter a valid URL';
          }
        }
      }
    ]);

    // Test connection and get available models
    console.log(chalk.blue('🔍 Checking Ollama connection and available models...\n'));
    
    try {
      const ollama = new OllamaProvider(baseUrl, this.errorHandler);
      const isAvailable = await ollama.isAvailable();
      
      if (!isAvailable) {
        console.log(chalk.yellow('⚠️  Could not connect to Ollama. Please make sure it\'s running.'));
        console.log(chalk.gray('You can continue setup and configure models later.\n'));
        
        return { baseUrl, model: 'llama2' }; // Default fallback
      }

      const models = await ollama.getModels();
      
      if (models.length === 0) {
        console.log(chalk.yellow('⚠️  No models found in Ollama.'));
        console.log(chalk.gray('You can pull models later using: ollama pull <model-name>\n'));
        
        const { shouldPull } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'shouldPull',
            message: 'Would you like to pull a recommended model now? (This may take a while)',
            default: false
          }
        ]);

        if (shouldPull) {
          const { modelToPull } = await inquirer.prompt([
            {
              type: 'list',
              name: 'modelToPull',
              message: 'Choose a model to pull:',
              choices: [
                { name: 'llama2 (7B - Good balance)', value: 'llama2' },
                { name: 'llama2:13b (13B - Better quality)', value: 'llama2:13b' },
                { name: 'codellama (7B - Code focused)', value: 'codellama' },
                { name: 'mistral (7B - Fast and efficient)', value: 'mistral' }
              ]
            }
          ]);

          console.log(chalk.blue(`📥 Pulling model: ${modelToPull}...`));
          console.log(chalk.gray('This may take several minutes depending on your internet connection.\n'));
          
          try {
            await ollama.pullModel(modelToPull);
            console.log(chalk.green(`✅ Model ${modelToPull} pulled successfully!\n`));
            return { baseUrl, model: modelToPull };
          } catch (error) {
            console.log(chalk.red(`❌ Failed to pull model: ${(error as Error).message}`));
            console.log(chalk.gray('You can pull models manually later.\n'));
            return { baseUrl, model: modelToPull };
          }
        }

        return { baseUrl, model: 'llama2' };
      }

      const { model } = await inquirer.prompt([
        {
          type: 'list',
          name: 'model',
          message: 'Choose your preferred model:',
          choices: models.map(m => ({ name: m, value: m })),
          default: models[0]
        }
      ]);

      console.log(chalk.green(`✅ Ollama configured with model: ${model}\n`));
      return { baseUrl, model };

    } catch (error) {
      console.log(chalk.red(`❌ Error connecting to Ollama: ${(error as Error).message}`));
      console.log(chalk.gray('Continuing with default configuration.\n'));
      return { baseUrl, model: 'llama2' };
    }
  }

  private async testConfiguration(config: OnboardingResult): Promise<{ success: boolean; error?: string }> {
    console.log(chalk.bold('Step 3: Testing Configuration\n'));
    console.log(chalk.blue('🧪 Testing connection to your AI provider...\n'));

    try {
      let provider;
      
      if (config.provider === 'deepseek') {
        provider = new DeepseekProvider(config.apiKey, this.errorHandler);
      } else if (config.provider === 'ollama') {
        provider = new OllamaProvider(config.baseUrl || 'http://localhost:11434', this.errorHandler);
      } else {
        throw new Error(`Unknown provider: ${config.provider}`);
      }

      const isAvailable = await provider.isAvailable();
      
      if (isAvailable) {
        console.log(chalk.green('✅ Connection test successful!\n'));
        return { success: true };
      } else {
        return { success: false, error: 'Provider is not available' };
      }
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  private async configurePreferences(): Promise<void> {
    console.log(chalk.bold('Step 4: Configure Preferences\n'));

    const preferences = await inquirer.prompt([
      {
        type: 'list',
        name: 'theme',
        message: 'Choose your preferred theme:',
        choices: [
          { name: '🌙 Dark (Recommended)', value: 'dark' },
          { name: '☀️  Light', value: 'light' }
        ],
        default: 'dark'
      },
      {
        type: 'confirm',
        name: 'autoApprove',
        message: 'Auto-approve safe shell commands? (You can change this later)',
        default: false
      },
      {
        type: 'confirm',
        name: 'saveHistory',
        message: 'Save conversation history?',
        default: true
      },
      {
        type: 'confirm',
        name: 'showTimestamps',
        message: 'Show timestamps in conversations?',
        default: true
      }
    ]);

    // Apply preferences
    this.configManager.setTheme(preferences.theme);
    this.configManager.setAutoApprove(preferences.autoApprove);
    this.configManager.setSaveHistory(preferences.saveHistory);
    this.configManager.setShowTimestamps(preferences.showTimestamps);

    console.log(chalk.green('✅ Preferences configured!\n'));
  }

  private saveConfiguration(config: OnboardingResult): void {
    console.log(chalk.bold('Step 5: Saving Configuration\n'));

    this.configManager.setProvider(config.provider);
    this.configManager.setModel(config.model);
    
    if (config.apiKey) {
      this.configManager.setApiKey(config.apiKey);
    }
    
    if (config.baseUrl) {
      this.configManager.setBaseUrl(config.baseUrl);
    }

    console.log(chalk.green('✅ Configuration saved!\n'));
  }

  private showCompletionMessage(): void {
    console.log(chalk.green.bold('🎉 Setup Complete!\n'));
    console.log(chalk.white('You\'re all set to start using Arien AI!\n'));
    
    console.log(chalk.bold('Quick Start Commands:\n'));
    console.log(chalk.blue('  • Type your requests in natural language'));
    console.log(chalk.blue('  • Use /help to see all available commands'));
    console.log(chalk.blue('  • Press F1 anytime for help'));
    console.log(chalk.blue('  • Use /config to view or change settings\n'));
    
    console.log(chalk.bold('Example Requests:\n'));
    console.log(chalk.gray('  "List all files in the current directory"'));
    console.log(chalk.gray('  "Check system memory usage"'));
    console.log(chalk.gray('  "Install npm package express"'));
    console.log(chalk.gray('  "Show git status and recent commits"\n'));
    
    console.log(chalk.yellow('Starting Arien AI in 3 seconds...\n'));
  }

  public async isOnboardingNeeded(): Promise<boolean> {
    const config = this.configManager.getConfig();

    // Check if basic configuration is missing
    if (!config.provider) return true;

    // Check if Deepseek is selected but no API key is configured
    if (config.provider === 'deepseek' && !config.apiKey) return true;

    // Check if Ollama is selected but not available
    if (config.provider === 'ollama') {
      try {
        const ollama = new OllamaProvider(config.baseUrl || 'http://localhost:11434', this.errorHandler);
        const isAvailable = await ollama.isAvailable();
        if (!isAvailable) {
          // Ollama is not running, but we can still continue
          // Just show a warning and don't force onboarding
          return false;
        }
      } catch (error) {
        // Ollama connection failed, but we can still continue
        return false;
      }
    }

    return false;
  }

  public async runQuickSetup(): Promise<void> {
    console.log(chalk.cyan.bold('\n🚀 Quick Setup\n'));
    
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to do?',
        choices: [
          { name: '⚡ Quick start with Ollama (local)', value: 'ollama' },
          { name: '🔑 Set up Deepseek API key', value: 'deepseek' },
          { name: '⚙️  Full configuration setup', value: 'full' },
          { name: '➡️  Skip setup for now', value: 'skip' }
        ]
      }
    ]);

    switch (action) {
      case 'ollama':
        this.configManager.setProvider('ollama');
        this.configManager.setModel('llama2');
        console.log(chalk.green('✅ Quick setup complete! Using Ollama with default settings.'));
        break;
        
      case 'deepseek':
        const { apiKey } = await inquirer.prompt([
          {
            type: 'password',
            name: 'apiKey',
            message: 'Enter your Deepseek API key:',
            mask: '*'
          }
        ]);
        this.configManager.setProvider('deepseek');
        this.configManager.setModel('deepseek-chat');
        this.configManager.setApiKey(apiKey);
        console.log(chalk.green('✅ Deepseek configured successfully!'));
        break;
        
      case 'full':
        await this.run();
        break;
        
      case 'skip':
        console.log(chalk.yellow('⏭️  Setup skipped. You can configure later using /config commands.'));
        break;
    }
  }
}
