import blessed from 'blessed';
import { TerminalConfig } from '../../types/index.js';

export class TerminalLayout {
  public screen!: blessed.Widgets.Screen;
  public chatBox!: blessed.Widgets.Log;
  public inputBox!: blessed.Widgets.TextboxElement;
  public headerBox!: blessed.Widgets.BoxElement;
  public statusBox!: blessed.Widgets.BoxElement;
  public helpBox!: blessed.Widgets.BoxElement;
  private config: TerminalConfig;

  constructor(config: TerminalConfig) {
    this.config = config;
    this.initializeScreen();
    this.createLayout();
    this.setupKeyBindings();
  }

  private initializeScreen(): void {
    this.screen = blessed.screen({
      smartCSR: true,
      title: 'Arien AI - Intelligent CLI Assistant',
      cursor: {
        artificial: true,
        shape: 'line',
        blink: true,
        color: 'white'
      },
      debug: false,
      dockBorders: false,
      fullUnicode: true
    });

    // Apply theme
    this.applyTheme();
  }

  private createLayout(): void {
    // Header box
    this.headerBox = blessed.box({
      parent: this.screen,
      top: 0,
      left: 0,
      width: '100%',
      height: 3,
      content: this.getHeaderContent(),
      tags: true,
      border: {
        type: 'line'
      },
      style: {
        fg: this.config.theme === 'dark' ? 'cyan' : 'blue',
        bg: this.config.theme === 'dark' ? 'black' : 'white',
        border: {
          fg: this.config.theme === 'dark' ? 'cyan' : 'blue'
        }
      }
    });

    // Chat/conversation box
    this.chatBox = blessed.log({
      parent: this.screen,
      top: 3,
      left: 0,
      width: '100%',
      height: '100%-6',
      tags: true,
      scrollable: true,
      alwaysScroll: true,
      scrollOnInput: false,
      padding: {
        left: 1,
        right: 1,
        top: 0,
        bottom: 0
      },
      scrollbar: {
        ch: ' ',
        track: {
          bg: this.config.theme === 'dark' ? 'grey' : 'lightgrey'
        },
        style: {
          inverse: true
        }
      },
      border: {
        type: 'line'
      },
      style: {
        fg: this.config.theme === 'dark' ? 'white' : 'black',
        bg: this.config.theme === 'dark' ? 'black' : 'white',
        border: {
          fg: this.config.theme === 'dark' ? 'grey' : 'grey'
        }
      }
    });

    // Input box
    this.inputBox = blessed.textbox({
      parent: this.screen,
      bottom: 3,
      left: 0,
      width: '100%',
      height: 3,
      input: true,
      keys: true,
      mouse: true,
      inputOnFocus: true,
      content: '',
      tags: true,
      padding: {
        left: 1,
        right: 1,
        top: 0,
        bottom: 0
      },
      border: {
        type: 'line'
      },
      style: {
        fg: this.config.theme === 'dark' ? 'white' : 'black',
        bg: this.config.theme === 'dark' ? 'black' : 'white',
        border: {
          fg: this.config.theme === 'dark' ? 'green' : 'green'
        },
        focus: {
          border: {
            fg: this.config.theme === 'dark' ? 'yellow' : 'blue'
          }
        }
      }
    });

    // Status box
    this.statusBox = blessed.box({
      parent: this.screen,
      bottom: 0,
      left: 0,
      width: '100%',
      height: 3,
      content: this.getStatusContent(),
      tags: true,
      border: {
        type: 'line'
      },
      style: {
        fg: this.config.theme === 'dark' ? 'grey' : 'grey',
        bg: this.config.theme === 'dark' ? 'black' : 'white',
        border: {
          fg: this.config.theme === 'dark' ? 'grey' : 'grey'
        }
      }
    });

    // Help box (initially hidden)
    this.helpBox = blessed.box({
      parent: this.screen,
      top: 'center',
      left: 'center',
      width: '80%',
      height: '80%',
      content: this.getHelpContent(),
      tags: true,
      border: {
        type: 'line'
      },
      style: {
        fg: this.config.theme === 'dark' ? 'white' : 'black',
        bg: this.config.theme === 'dark' ? 'black' : 'white',
        border: {
          fg: this.config.theme === 'dark' ? 'yellow' : 'blue'
        }
      },
      hidden: true,
      scrollable: true,
      alwaysScroll: true
    });

    // Focus on input by default
    this.inputBox.focus();
  }

  private setupKeyBindings(): void {
    // Global key bindings
    this.screen.key(['escape', 'q', 'C-c'], () => {
      return process.exit(0);
    });

    this.screen.key(['f1', '?'], () => {
      this.toggleHelp();
    });

    this.screen.key(['f2'], () => {
      this.toggleTheme();
    });

    this.screen.key(['f5'], () => {
      this.clearChat();
    });

    // Input box specific bindings
    this.inputBox.key(['up'], () => {
      // Navigate chat history up
      this.emit('history-up');
    });

    this.inputBox.key(['down'], () => {
      // Navigate chat history down
      this.emit('history-down');
    });

    this.inputBox.key(['tab'], () => {
      // Auto-complete
      this.emit('autocomplete');
    });

    this.inputBox.key(['enter'], () => {
      const input = this.inputBox.getValue();
      if (input.trim()) {
        this.emit('submit', input.trim());
        this.inputBox.clearValue();
      }
    });

    // Chat box scrolling
    this.chatBox.key(['pageup'], () => {
      this.chatBox.scroll(-10);
      this.screen.render();
    });

    this.chatBox.key(['pagedown'], () => {
      this.chatBox.scroll(10);
      this.screen.render();
    });
  }

  private applyTheme(): void {
    // Apply theme through blessed options instead of direct style assignment
    if (this.config.theme === 'dark') {
      (this.screen as any).options = {
        ...(this.screen as any).options,
        fg: 'white',
        bg: 'black'
      };
    } else {
      (this.screen as any).options = {
        ...(this.screen as any).options,
        fg: 'black',
        bg: 'white'
      };
    }
  }

  private getHeaderContent(): string {
    const provider = this.config.provider.toUpperCase();
    const model = this.config.model;
    const cwd = process.cwd();
    
    return `{center}{bold}🤖 Arien AI - Intelligent CLI Assistant{/bold}{/center}\n` +
           `{center}Provider: {cyan-fg}${provider}{/cyan-fg} | Model: {green-fg}${model}{/green-fg} | Directory: {yellow-fg}${cwd}{/yellow-fg}{/center}`;
  }

  private getStatusContent(): string {
    const timestamp = new Date().toLocaleTimeString();
    return `{left}Press F1 for help | F2 to toggle theme | F5 to clear | Ctrl+C to exit{/left}{right}${timestamp}{/right}`;
  }

  private getHelpContent(): string {
    return `{center}{bold}🤖 Arien AI - Help{/bold}{/center}\n\n` +
           `{bold}Slash Commands:{/bold}\n` +
           `  /model <name>     - Switch to a different model\n` +
           `  /provider <name>  - Switch to a different provider\n` +
           `  /session <name>   - Create or switch to a session\n` +
           `  /history          - Show message history\n` +
           `  /clear            - Clear the current conversation\n` +
           `  /config           - Show current configuration\n` +
           `  /help             - Show this help message\n` +
           `  /exit             - Exit the application\n\n` +
           `{bold}Key Bindings:{/bold}\n` +
           `  Enter             - Send message\n` +
           `  Up/Down Arrows    - Navigate message history\n` +
           `  Tab               - Auto-complete (when available)\n` +
           `  Page Up/Down      - Scroll chat history\n` +
           `  F1 or ?           - Toggle this help\n` +
           `  F2                - Toggle dark/light theme\n` +
           `  F5                - Clear conversation\n` +
           `  Ctrl+C or Escape  - Exit application\n\n` +
           `{bold}Features:{/bold}\n` +
           `  • Execute shell commands safely\n` +
           `  • Real-time streaming responses\n` +
           `  • Intelligent error handling and retry logic\n` +
           `  • Session management and history\n` +
           `  • Multiple LLM provider support\n` +
           `  • Function calling for system operations\n\n` +
           `{bold}Examples:{/bold}\n` +
           `  "List all files in the current directory"\n` +
           `  "Check system memory usage"\n` +
           `  "Install npm package express"\n` +
           `  "Show git status and recent commits"\n` +
           `  "Find all Python files in this project"\n\n` +
           `Press F1 or Escape to close this help.`;
  }

  public updateHeader(provider: string, model: string): void {
    this.config.provider = provider;
    this.config.model = model;
    this.headerBox.setContent(this.getHeaderContent());
    this.screen.render();
  }

  public updateStatus(): void {
    this.statusBox.setContent(this.getStatusContent());
    this.screen.render();
  }

  public appendMessage(message: string): void {
    // Ensure proper line breaks and formatting
    const formattedMessage = message
      .replace(/\r\n/g, '\n')  // Normalize line endings
      .replace(/\r/g, '\n')    // Handle old Mac line endings
      .trim();                 // Remove leading/trailing whitespace

    // Split message into lines and add each line separately for better formatting
    const lines = formattedMessage.split('\n');
    lines.forEach(line => {
      this.chatBox.log(line);
    });

    // Add an empty line for spacing between messages
    this.chatBox.log('');

    this.screen.render();
  }

  public clearChat(): void {
    this.chatBox.setContent('');
    this.screen.render();
  }

  public toggleHelp(): void {
    if (this.helpBox.hidden) {
      this.helpBox.show();
      this.helpBox.focus();
    } else {
      this.helpBox.hide();
      this.inputBox.focus();
    }
    this.screen.render();
  }

  public toggleTheme(): void {
    this.config.theme = this.config.theme === 'dark' ? 'light' : 'dark';
    this.applyTheme();
    this.screen.render();
  }

  public setInputPlaceholder(placeholder: string): void {
    // Blessed doesn't have built-in placeholder support
    // This is a simple implementation
    if (!this.inputBox.getValue()) {
      this.inputBox.setValue(`{grey-fg}${placeholder}{/grey-fg}`);
    }
  }

  public focusInput(): void {
    this.inputBox.focus();
    this.screen.render();
  }

  public render(): void {
    this.screen.render();
  }

  public destroy(): void {
    this.screen.destroy();
  }

  // Event emitter functionality
  private listeners: Map<string, Function[]> = new Map();

  public on(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  public emit(event: string, ...args: any[]): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => listener(...args));
    }
  }
}
