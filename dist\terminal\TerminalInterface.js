import { TerminalLayout } from './components/TerminalLayout.js';
import { ThinkingSpinner } from './components/ThinkingSpinner.js';
import { SlashCommands } from './components/SlashCommands.js';
import { MessageHistory } from './components/MessageHistory.js';
import { OnboardingComponent } from './components/OnboardingComponent.js';
import { ChatInputProcessor } from './components/ChatInputProcessor.js';
import { ChatOutputProcessor } from './components/ChatOutputProcessor.js';
import { ToolCallsProcessor } from './components/ToolCallsProcessor.js';
import { ConfigManager } from '../config/ConfigManager.js';
import { ErrorHandler } from '../utils/ErrorHandler.js';
import { ShellTool } from '../tools/ShellTool.js';
import { DeepseekProvider } from '../providers/DeepseekProvider.js';
import { OllamaProvider } from '../providers/OllamaProvider.js';
import chalk from 'chalk';
import inquirer from 'inquirer';
export class TerminalInterface {
    layout;
    spinner;
    slashCommands;
    messageHistory;
    onboarding;
    inputProcessor;
    outputProcessor;
    toolProcessor;
    configManager;
    errorHandler;
    shellTool;
    providers = new Map();
    currentProvider = null;
    conversationHistory = [];
    isProcessing = false;
    currentSessionId = null;
    constructor() {
        this.configManager = new ConfigManager();
        this.errorHandler = new ErrorHandler(this.configManager.getRetryConfig());
        this.shellTool = new ShellTool(this.errorHandler);
        this.spinner = new ThinkingSpinner();
        this.slashCommands = new SlashCommands(this.configManager);
        this.messageHistory = new MessageHistory(this.configManager);
        this.onboarding = new OnboardingComponent(this.configManager);
        // Initialize new processors
        this.inputProcessor = new ChatInputProcessor(this.configManager, this.messageHistory, this.slashCommands);
        this.outputProcessor = new ChatOutputProcessor(this.configManager);
        this.toolProcessor = new ToolCallsProcessor(this.configManager, this.shellTool, this.errorHandler, {
            autoApprove: this.configManager.getConfig().autoApprove,
            enableParallelExecution: false,
            requireApprovalForDangerous: true
        });
        this.initializeProviders();
        this.layout = new TerminalLayout(this.configManager.getConfig());
        this.setupEventHandlers();
    }
    initializeProviders() {
        // Initialize Deepseek provider
        const deepseekProvider = new DeepseekProvider(this.configManager.getApiKey(), this.errorHandler);
        this.providers.set('deepseek', deepseekProvider);
        // Initialize Ollama provider
        const ollamaProvider = new OllamaProvider(this.configManager.getBaseUrl() || 'http://localhost:11434', this.errorHandler);
        this.providers.set('ollama', ollamaProvider);
        // Set current provider
        const currentProviderName = this.configManager.getProvider();
        this.currentProvider = this.providers.get(currentProviderName) || null;
    }
    setupEventHandlers() {
        // Layout event handlers
        this.layout.on('submit', async (input) => {
            await this.handleUserInput(input);
        });
        this.layout.on('history-up', () => {
            this.navigateHistory('up');
        });
        this.layout.on('history-down', () => {
            this.navigateHistory('down');
        });
        this.layout.on('autocomplete', () => {
            this.handleAutoComplete();
        });
        // Output processor event handlers
        this.outputProcessor.on('response_processed', (processed) => {
            // Handle processed responses if needed
        });
        this.outputProcessor.on('error_processed', (processed) => {
            // Handle processed errors if needed
        });
        this.outputProcessor.on('streaming_started', () => {
            this.spinner.startGenerating();
        });
        this.outputProcessor.on('streaming_finished', (processed) => {
            this.spinner.stop();
        });
        // Tool processor event handlers
        this.toolProcessor.on('processing_started', ({ toolCalls }) => {
            const message = this.outputProcessor.processSystemMessage(`Executing ${toolCalls.length} tool(s)...`, 'info');
            this.layout.appendMessage(message.formattedContent);
        });
        this.toolProcessor.on('tool_execution_started', ({ toolCall }) => {
            this.spinner.startExecuting();
        });
        this.toolProcessor.on('tool_execution_completed', ({ result }) => {
            // Tool execution completed - handled in main flow
        });
        this.toolProcessor.on('processing_completed', ({ results }) => {
            this.spinner.stop();
        });
        // Handle process termination gracefully
        process.on('SIGINT', () => {
            this.destroy();
            process.exit(0);
        });
        process.on('SIGTERM', () => {
            this.destroy();
            process.exit(0);
        });
    }
    async start() {
        // Check if onboarding is needed
        const needsOnboarding = await this.onboarding.isOnboardingNeeded();
        if (needsOnboarding) {
            console.clear();
            await this.onboarding.run();
            this.initializeProviders(); // Reinitialize with new config
            // Wait a moment before starting the interface
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        // Validate current provider
        await this.validateProvider();
        // Show welcome message
        this.showWelcomeMessage();
        // Start the interface
        this.layout.render();
    }
    async runOnboarding() {
        console.clear();
        console.log(chalk.cyan.bold('🤖 Welcome to Arien AI - Intelligent CLI Assistant'));
        console.log(chalk.gray('Let\'s set up your AI provider to get started.\n'));
        const { provider } = await inquirer.prompt([
            {
                type: 'list',
                name: 'provider',
                message: 'Choose your AI provider:',
                choices: [
                    { name: '🧠 Deepseek (Requires API key)', value: 'deepseek' },
                    { name: '🏠 Ollama (Local, requires Ollama running)', value: 'ollama' }
                ]
            }
        ]);
        this.configManager.setProvider(provider);
        if (provider === 'deepseek') {
            const { apiKey } = await inquirer.prompt([
                {
                    type: 'password',
                    name: 'apiKey',
                    message: 'Enter your Deepseek API key:',
                    mask: '*',
                    validate: (input) => {
                        if (!input.trim()) {
                            return 'API key is required';
                        }
                        return true;
                    }
                }
            ]);
            this.configManager.setApiKey(apiKey);
            const { model } = await inquirer.prompt([
                {
                    type: 'list',
                    name: 'model',
                    message: 'Choose your model:',
                    choices: [
                        { name: 'deepseek-chat (Recommended)', value: 'deepseek-chat' },
                        { name: 'deepseek-reasoner (Advanced reasoning)', value: 'deepseek-reasoner' }
                    ]
                }
            ]);
            this.configManager.setModel(model);
        }
        else if (provider === 'ollama') {
            const { baseUrl } = await inquirer.prompt([
                {
                    type: 'input',
                    name: 'baseUrl',
                    message: 'Ollama base URL:',
                    default: 'http://localhost:11434'
                }
            ]);
            this.configManager.setBaseUrl(baseUrl);
            // Try to connect and get models
            const ollamaProvider = new OllamaProvider(baseUrl, this.errorHandler);
            const isAvailable = await ollamaProvider.isAvailable();
            if (!isAvailable) {
                console.log(chalk.yellow('⚠️  Could not connect to Ollama. Make sure Ollama is running.'));
                console.log(chalk.gray('You can start Ollama and pull models later.'));
            }
            else {
                const models = await ollamaProvider.getModels();
                if (models.length > 0) {
                    const { model } = await inquirer.prompt([
                        {
                            type: 'list',
                            name: 'model',
                            message: 'Choose your model:',
                            choices: models.map(m => ({ name: m, value: m }))
                        }
                    ]);
                    this.configManager.setModel(model);
                }
                else {
                    console.log(chalk.yellow('No models found. You can pull models using: ollama pull <model-name>'));
                    this.configManager.setModel('llama2'); // Default fallback
                }
            }
        }
        console.log(chalk.green('\n✅ Setup complete! Starting Arien AI...\n'));
        await new Promise(resolve => setTimeout(resolve, 1500));
    }
    async validateProvider() {
        const providerName = this.configManager.getProvider();
        this.currentProvider = this.providers.get(providerName) || null;
        if (!this.currentProvider) {
            this.layout.appendMessage(chalk.red(`❌ Provider "${providerName}" not found. Please check your configuration.`));
            return;
        }
        try {
            const isAvailable = await this.currentProvider.isAvailable();
            if (!isAvailable) {
                if (providerName === 'deepseek' && !this.configManager.getApiKey()) {
                    this.layout.appendMessage(chalk.yellow(`⚠️  Deepseek API key not configured. Use /set api-key <your-key> or restart to run setup.`));
                }
                else if (providerName === 'ollama') {
                    this.layout.appendMessage(chalk.yellow(`⚠️  Ollama is not running. Please start Ollama or switch to Deepseek provider.`));
                }
                else {
                    this.layout.appendMessage(chalk.yellow(`⚠️  Provider "${providerName}" is not available. Please check your configuration.`));
                }
            }
            else {
                this.layout.appendMessage(chalk.green(`✅ Provider "${providerName}" is ready and available.`));
            }
        }
        catch (error) {
            this.layout.appendMessage(chalk.red(`❌ Error connecting to provider "${providerName}": ${error.message}`));
        }
    }
    showWelcomeMessage() {
        const config = this.configManager.getConfig();
        const welcomeMessage = [
            chalk.cyan.bold('🤖 Welcome to Arien AI - Intelligent CLI Assistant'),
            '',
            chalk.green('✅ Ready to help you with:'),
            '  • Execute shell commands safely and intelligently',
            '  • Analyze system information and logs',
            '  • Automate development tasks',
            '  • Manage files and directories',
            '  • Handle errors with smart retry logic',
            '',
            chalk.blue('Current Configuration:'),
            `  Provider: ${chalk.yellow(config.provider)}`,
            `  Model: ${chalk.yellow(config.model)}`,
            `  Directory: ${chalk.yellow(process.cwd())}`,
            '',
            chalk.gray('Type your request or use /help for commands. Press F1 for help anytime.')
        ].join('\n');
        this.layout.appendMessage(welcomeMessage);
    }
    async handleUserInput(input) {
        if (this.isProcessing) {
            this.layout.appendMessage(chalk.yellow('⚠️  Please wait for the current operation to complete.'));
            return;
        }
        try {
            // Process input using the input processor
            const processedInput = this.inputProcessor.processInput(input);
            // Validate input for security
            const validation = this.inputProcessor.validateInput(input);
            if (!validation.isValid) {
                for (const warning of validation.warnings) {
                    this.layout.appendMessage(chalk.yellow(`⚠️  ${warning}`));
                }
                const { proceed } = await inquirer.prompt([
                    {
                        type: 'confirm',
                        name: 'proceed',
                        message: 'Do you want to proceed anyway?',
                        default: false
                    }
                ]);
                if (!proceed) {
                    return;
                }
            }
            // Display formatted input
            const formattedInput = this.inputProcessor.formatInputForDisplay(processedInput);
            this.layout.appendMessage(formattedInput);
            if (processedInput.type === 'empty') {
                return;
            }
            if (processedInput.type === 'command') {
                const result = await this.slashCommands.executeCommand(input);
                this.layout.appendMessage(result.message);
                if (result.shouldExit) {
                    process.exit(0);
                }
                if (result.shouldClear) {
                    this.layout.clearChat();
                    this.conversationHistory = [];
                }
                if (result.configChanged) {
                    this.handleConfigChange();
                }
                return;
            }
            // Handle regular AI conversation
            await this.processAIRequest(processedInput.content);
        }
        catch (error) {
            const processedError = this.outputProcessor.processError(error, 'Input Processing');
            this.layout.appendMessage(processedError.formattedContent);
        }
    }
    async processAIRequest(userInput) {
        if (!this.currentProvider) {
            const errorOutput = this.outputProcessor.processError(new Error('No AI provider available. Please check your configuration.'), 'Provider Check');
            this.layout.appendMessage(errorOutput.formattedContent);
            return;
        }
        this.isProcessing = true;
        try {
            // Add user message to history
            const userMessage = {
                role: 'user',
                content: userInput
            };
            this.conversationHistory.push(userMessage);
            this.messageHistory.addEntry(userMessage, undefined, this.currentSessionId || undefined);
            // Prepare messages for AI
            const messages = this.prepareMessages();
            // Start AI thinking spinner
            this.spinner.startThinking();
            // Stream AI response
            await this.streamAIResponse(messages);
        }
        catch (error) {
            this.spinner.fail('AI request failed');
            const errorOutput = this.outputProcessor.processError(error, 'AI Request');
            this.layout.appendMessage(errorOutput.formattedContent);
            this.errorHandler.handleError(error, {
                operation: 'ai_request',
                provider: this.configManager.getProvider(),
                model: this.configManager.getModel(),
                timestamp: new Date(),
                retryCount: 0
            });
        }
        finally {
            this.isProcessing = false;
        }
    }
    prepareMessages() {
        const systemPrompt = this.getDefaultSystemPrompt();
        const messages = [
            {
                role: 'system',
                content: systemPrompt
            },
            ...this.conversationHistory
        ];
        return messages;
    }
    async streamAIResponse(messages) {
        if (!this.currentProvider)
            return;
        const config = this.configManager.getConfig();
        let fullResponse = '';
        let currentToolCalls = [];
        try {
            // Setup streaming with output processor
            this.outputProcessor.on('stream_chunk', (chunk) => {
                this.layout.appendMessage(chunk.content);
            });
            this.outputProcessor.on('streaming_finished', (processed) => {
                this.spinner.stop();
            });
            const stream = this.currentProvider.streamResponse(messages, {
                temperature: config.temperature,
                maxTokens: config.maxTokens,
                tools: [this.shellTool.getToolDefinition()],
                toolChoice: 'auto'
            });
            for await (const chunk of stream) {
                // Process chunk through output processor
                this.outputProcessor.processStreamChunk(chunk);
                if (chunk.content) {
                    fullResponse += chunk.content;
                }
                if (chunk.toolCalls) {
                    currentToolCalls.push(...chunk.toolCalls);
                }
                if (chunk.done) {
                    break;
                }
            }
            // Handle tool calls if present
            if (currentToolCalls.length > 0) {
                await this.handleToolCalls(currentToolCalls);
            }
            // Add assistant response to history
            const assistantMessage = {
                role: 'assistant',
                content: fullResponse,
                toolCalls: currentToolCalls.length > 0 ? currentToolCalls : undefined
            };
            this.conversationHistory.push(assistantMessage);
            this.messageHistory.addEntry(messages[messages.length - 1], // Last user message
            assistantMessage, this.currentSessionId || undefined);
        }
        catch (error) {
            this.spinner.fail();
            throw error;
        }
    }
    async handleToolCalls(toolCalls) {
        try {
            this.spinner.startExecuting();
            // Process tool calls using the tool processor
            const results = await this.toolProcessor.processToolCalls(toolCalls);
            // Display results
            const formattedResults = this.toolProcessor.formatResults(results);
            this.layout.appendMessage(formattedResults);
            // Add tool results to conversation history
            for (const result of results) {
                const toolResultMessage = {
                    role: 'tool',
                    content: JSON.stringify(result.result),
                    toolCallId: result.toolCall.id
                };
                this.conversationHistory.push(toolResultMessage);
            }
            // Check if any critical tools failed and need follow-up
            const failedCriticalTools = results.filter(r => !r.success && this.isCriticalTool(r.toolCall));
            if (failedCriticalTools.length > 0) {
                const systemMessage = this.outputProcessor.processSystemMessage('Some commands failed. Let me help you resolve the issues...', 'warning');
                this.layout.appendMessage(systemMessage.formattedContent);
                // Continue conversation with error context
                const messages = this.prepareMessages();
                await this.streamAIResponse(messages);
            }
        }
        catch (error) {
            const errorOutput = this.outputProcessor.processError(error, 'Tool Execution');
            this.layout.appendMessage(errorOutput.formattedContent);
        }
        finally {
            this.spinner.stop();
        }
    }
    isCriticalTool(toolCall) {
        // Define which tools are critical and should trigger follow-up on failure
        const criticalTools = ['execute_shell_command'];
        return criticalTools.includes(toolCall.function.name);
    }
    async requestCommandApproval(command) {
        // In a blessed-based interface, we need to handle this differently
        // For now, we'll use a simple approach
        this.layout.appendMessage(chalk.yellow(`\n⚠️  Command requires approval: ${command}`));
        this.layout.appendMessage(chalk.gray('Press Y to approve, N to cancel:'));
        // This is a simplified implementation
        // In a real implementation, you'd capture key input
        return true; // For now, auto-approve
    }
    handleConfigChange() {
        // Reinitialize providers with new config
        this.initializeProviders();
        // Update layout header
        const config = this.configManager.getConfig();
        this.layout.updateHeader(config.provider, config.model);
        this.layout.appendMessage(chalk.green('✅ Configuration updated successfully.'));
    }
    navigateHistory(direction) {
        const historyEntry = this.inputProcessor.navigateHistory(direction);
        if (historyEntry) {
            // Update input box with history entry
            // This would be implemented with the blessed input box
            this.layout.appendMessage(chalk.gray(`History: ${historyEntry}`));
        }
    }
    handleAutoComplete() {
        // Get current input from layout
        const currentInput = ''; // This would come from the input box
        const suggestions = this.inputProcessor.getAutoCompleteSuggestions(currentInput);
        if (suggestions.length > 0) {
            this.layout.appendMessage(chalk.blue('Suggestions: ') + suggestions.join(', '));
        }
    }
    getDefaultSystemPrompt() {
        return `You are Arien AI, an intelligent CLI assistant that can execute shell commands to help users accomplish their tasks.

CORE CAPABILITIES:
- Execute shell commands safely and efficiently using the execute_shell_command tool
- Analyze command outputs and provide insights
- Handle errors gracefully with intelligent retry logic
- Provide step-by-step guidance for complex tasks
- Maintain context across multiple command executions
- Stream responses in real-time for better user experience

SHELL TOOL USAGE GUIDELINES:

WHEN TO USE THE SHELL TOOL:
✅ File operations (create, read, modify, delete files and directories)
✅ System information gathering (ps, top, df, free, uname, etc.)
✅ Package management (npm, pip, apt, yum, brew, etc.)
✅ Git operations (status, commit, push, pull, clone, etc.)
✅ Development tasks (build, test, deploy, start servers)
✅ Network operations (ping, curl, wget, ssh, etc.)
✅ Text processing (grep, sed, awk, sort, uniq, etc.)
✅ Archive operations (tar, zip, unzip, etc.)
✅ Process management (kill, jobs, nohup, etc.)

WHEN NOT TO USE THE SHELL TOOL:
❌ Simple calculations (use your built-in capabilities)
❌ Text formatting or markdown generation
❌ Explaining concepts without system interaction
❌ Code review or analysis of provided code snippets
❌ General questions that don't require system access

EXECUTION STRATEGIES:

SEQUENTIAL EXECUTION (Default):
- Use for dependent commands where order matters
- Each command waits for the previous to complete
- Safer approach for critical operations
- Example: cd project && npm install && npm start

PARALLEL EXECUTION:
- Use for independent operations that can run simultaneously
- Faster for bulk operations
- Use with caution for system-modifying commands
- Example: Multiple file downloads or independent checks

SAFETY AND APPROVAL:

DANGEROUS COMMANDS (Require Approval):
- rm -rf, rmdir with important directories
- sudo commands that modify system files
- chmod/chown on system directories
- dd, mkfs, fdisk (disk operations)
- Commands affecting /etc, /usr, /var system directories
- Network configuration changes
- Service stop/start for critical services

AUTO-APPROVED COMMANDS:
- Read-only operations (ls, cat, grep, find)
- Status checks (ps, top, df, free, git status)
- Safe development commands (npm install, pip install)
- File creation in user directories
- Non-destructive text processing

ERROR HANDLING AND RETRY LOGIC:

RETRY SCENARIOS:
- Network timeouts (curl, wget, git operations)
- Temporary resource unavailability
- Rate limiting from APIs or services
- Transient file system issues

NO RETRY SCENARIOS:
- Permission denied errors
- File not found errors
- Syntax errors in commands
- Hardware failures

COMMUNICATION GUIDELINES:

BEFORE EXECUTION:
1. Explain what you're about to do in simple terms
2. Mention if the command might take time or require approval
3. Warn about any potential risks or side effects

DURING EXECUTION:
1. Show the exact command being executed
2. Provide real-time updates for long-running operations
3. Display progress indicators when possible

AFTER EXECUTION:
1. Summarize what was accomplished
2. Explain any errors or warnings that occurred
3. Suggest next steps or follow-up actions
4. Provide relevant insights from the output

EXAMPLES:

Good: "I'll check your system memory usage using the 'free -h' command to see available RAM."
Bad: "Let me run a command." (too vague)

Good: "I'll install the express package using 'npm install express'. This is safe and won't modify system files."
Bad: "Installing express..." (no explanation of safety)

Good: "The command failed with exit code 1. The error suggests a permission issue. Let me try with sudo or check file permissions."
Bad: "Command failed." (no analysis or next steps)

Remember: Always prioritize user safety, provide clear explanations, and handle errors gracefully with intelligent retry logic and helpful suggestions.`;
    }
    destroy() {
        this.spinner.stop();
        this.layout.destroy();
    }
}
//# sourceMappingURL=TerminalInterface.js.map